#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查1.txt中实际的节点数量
"""

import base64

def check_nodes():
    with open('1.txt', 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    print(f"Base64字符串长度: {len(content)}")
    
    # 解码base64
    decoded = base64.b64decode(content).decode('utf-8')
    print(f"解码后内容长度: {len(decoded)}")
    
    # 按行分割
    lines = decoded.split('\n')
    print(f"总行数: {len(lines)}")
    
    # 过滤空行
    non_empty_lines = [line.strip() for line in lines if line.strip()]
    print(f"非空行数: {len(non_empty_lines)}")
    
    # 统计各种协议的节点
    protocols = {}
    valid_nodes = []
    
    for i, line in enumerate(non_empty_lines, 1):
        line = line.strip()
        if line.startswith(('ss://', 'ssr://', 'vmess://', 'vless://', 'trojan://', 'hysteria2://', 'hy2://')):
            protocol = line.split('://')[0]
            protocols[protocol] = protocols.get(protocol, 0) + 1
            valid_nodes.append(line)
            print(f"{i:2d}: {protocol:10s} - {line[:80]}...")
        else:
            print(f"{i:2d}: INVALID    - {line[:80]}...")
    
    print(f"\n协议统计:")
    for protocol, count in protocols.items():
        print(f"  {protocol}: {count} 个")
    
    print(f"\n有效节点总数: {len(valid_nodes)}")
    return valid_nodes

if __name__ == '__main__':
    check_nodes()
