#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试解析过程，找出为什么丢失了5个节点
"""

import base64
import re
import urllib.parse
import json
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DebugProxyConverter:
    def __init__(self):
        self.supported_protocols = ['ss', 'ssr', 'vmess', 'vless', 'trojan', 'hysteria2', 'hy2']
        self.base64_pattern = re.compile(r'[A-Za-z0-9+/=]{20,}')
        self.proxy_pattern = re.compile(r'(ss|ssr|vmess|vless|trojan|hysteria2|hy2)://')

    def is_base64(self, s: str) -> bool:
        """检查字符串是否为有效的base64编码"""
        try:
            s = s.strip()
            if len(s) == 0:
                return False

            # 处理URL安全的base64
            s = s.replace('-', '+').replace('_', '/')

            # 添加缺失的填充
            if len(s) % 4 != 0:
                s += '=' * (4 - len(s) % 4)

            # 检查是否包含非base64字符（不包括填充字符）
            valid_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
            s_without_padding = s.rstrip('=')
            for c in s_without_padding:
                if c not in valid_chars:
                    return False

            # 尝试解码
            decoded = base64.b64decode(s)
            # 检查解码后的内容是否为有效的UTF-8字符串
            decoded.decode('utf-8')
            return True
        except Exception:
            return False

    def decode_base64(self, s: str) -> str:
        """解码base64字符串"""
        if not self.is_base64(s):
            return s

        try:
            # 处理URL安全的base64
            s = s.strip()
            s = s.replace('-', '+').replace('_', '/')

            # 添加缺失的填充
            if len(s) % 4 != 0:
                s += '=' * (4 - len(s) % 4)

            decoded = base64.b64decode(s)
            decoded_str = decoded.decode('utf-8')

            # 检查解码后的内容是否为有效的UTF-8字符串
            if not decoded_str.isprintable() and len([c for c in decoded_str if ord(c) > 127]) > len(decoded_str) * 0.3:
                # 如果包含太多非ASCII字符，可能不是有效的文本
                return s

            return decoded_str
        except Exception as e:
            logger.debug(f"Base64解码失败: {e}")
            return s

    def parse_proxy(self, proxy_url: str) -> dict:
        """简化的解析代理节点URL"""
        proxy_url = proxy_url.strip()
        if not proxy_url:
            return None

        if proxy_url.startswith('ss://'):
            return {'type': 'ss', 'url': proxy_url}
        elif proxy_url.startswith('vmess://'):
            return {'type': 'vmess', 'url': proxy_url}
        elif proxy_url.startswith('trojan://'):
            return {'type': 'trojan', 'url': proxy_url}
        elif proxy_url.startswith('vless://'):
            return {'type': 'vless', 'url': proxy_url}
        elif proxy_url.startswith(('hysteria2://', 'hy2://')):
            return {'type': 'hysteria2', 'url': proxy_url}

        return None

    def extract_proxies_from_base64(self, base64_str: str) -> list:
        """从base64字符串中提取代理节点"""
        proxies = []
        try:
            decoded = self.decode_base64(base64_str)
            logger.info(f"解码后的内容长度: {len(decoded)}")

            # 按行分割解码后的内容
            lines = decoded.split('\n')
            logger.info(f"总行数: {len(lines)}")
            
            valid_lines = []
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    logger.debug(f"第{i}行: 空行，跳过")
                    continue
                
                valid_lines.append(line)
                logger.debug(f"第{i}行: {line[:80]}...")

                # 检查是否是代理URL
                if self.proxy_pattern.match(line):
                    proxy = self.parse_proxy(line)
                    if proxy:
                        proxies.append(proxy)
                        logger.info(f"✓ 第{i}行: 成功解析 {proxy['type']} 节点")
                    else:
                        logger.warning(f"✗ 第{i}行: 解析失败 - {line[:50]}...")
                else:
                    logger.warning(f"✗ 第{i}行: 不匹配代理模式 - {line[:50]}...")

            logger.info(f"有效行数: {len(valid_lines)}")
            logger.info(f"成功解析的节点数: {len(proxies)}")

        except Exception as e:
            logger.error(f"从base64提取代理失败: {e}")

        return proxies

    def debug_1_txt(self):
        """调试1.txt文件的解析过程"""
        logger.info("开始调试1.txt文件的解析过程...")
        
        with open('1.txt', 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        logger.info(f"Base64字符串长度: {len(content)}")
        
        # 提取代理节点
        proxies = self.extract_proxies_from_base64(content)
        
        # 统计结果
        type_count = {}
        for proxy in proxies:
            proxy_type = proxy.get('type', 'unknown')
            type_count[proxy_type] = type_count.get(proxy_type, 0) + 1
        
        logger.info("解析结果统计:")
        for proxy_type, count in type_count.items():
            logger.info(f"  {proxy_type}: {count} 个")
        
        logger.info(f"总计解析出 {len(proxies)} 个节点")
        
        return proxies

if __name__ == '__main__':
    converter = DebugProxyConverter()
    converter.debug_1_txt()
