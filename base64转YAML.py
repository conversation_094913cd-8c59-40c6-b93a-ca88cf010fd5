#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Base64代理节点转换器
功能：
1. 搜索当前文件夹中所有txt文件中包含base64编码的文本
2. 提取后进行解码
3. 将解码后的代理节点转换为YAML格式文件
4. 支持节点去重功能
"""

import os
import re
import base64
import json
import yaml
import urllib.parse
from typing import List, Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProxyConverter:
    def __init__(self):
        self.supported_protocols = ['ss', 'ssr', 'vmess', 'vless', 'trojan', 'hysteria2', 'hy2']
        self.base64_pattern = re.compile(r'[A-Za-z0-9+/=]{20,}')
        self.proxy_pattern = re.compile(r'(ss|ssr|vmess|vless|trojan|hysteria2|hy2)://')

    def is_base64(self, s: str) -> bool:
        """检查字符串是否为有效的base64编码（参考项目中的实现）"""
        try:
            s = s.strip()
            if len(s) == 0:
                return False

            # 处理URL安全的base64
            s = s.replace('-', '+').replace('_', '/')

            # 添加缺失的填充
            if len(s) % 4 != 0:
                s += '=' * (4 - len(s) % 4)

            # 检查是否包含非base64字符（不包括填充字符）
            valid_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
            s_without_padding = s.rstrip('=')
            for c in s_without_padding:
                if c not in valid_chars:
                    return False

            # 尝试解码
            decoded = base64.b64decode(s)
            # 检查解码后的内容是否为有效的UTF-8字符串
            decoded.decode('utf-8')
            return True
        except Exception:
            return False

    def decode_base64(self, s: str) -> str:
        """解码base64字符串，如果不是base64则返回原始字符串（参考项目中的实现）"""
        if not self.is_base64(s):
            return s

        try:
            # 处理URL安全的base64
            s = s.strip()
            s = s.replace('-', '+').replace('_', '/')

            # 添加缺失的填充
            if len(s) % 4 != 0:
                s += '=' * (4 - len(s) % 4)

            decoded = base64.b64decode(s)
            decoded_str = decoded.decode('utf-8')

            # 检查解码后的内容是否为有效的UTF-8字符串
            if not decoded_str.isprintable() and len([c for c in decoded_str if ord(c) > 127]) > len(decoded_str) * 0.3:
                # 如果包含太多非ASCII字符，可能不是有效的文本
                return s

            return decoded_str
        except Exception as e:
            logger.debug(f"Base64解码失败: {e}")
            return s

    def parse_ss(self, url: str) -> Optional[Dict[str, Any]]:
        """解析Shadowsocks节点（参考项目中的实现）"""
        if not url.startswith('ss://'):
            return None

        try:
            data = url[5:]  # 移除 ss:// 前缀

            # 检查是否包含@分隔符
            if '@' not in data:
                if '#' in data:
                    temp = data.split('#', 1)
                    data = self.decode_base64(temp[0]) + '#' + temp[1]
                else:
                    data = self.decode_base64(data)

            # 判断是否包含 @ #
            if '@' not in data and '#' not in data:
                logger.debug(f"SS格式错误: 缺少@或#分隔符 - {url}")
                return None

            # 分离名称部分
            name = ""
            if '#' in data:
                idx = data.rfind('#')
                name = data[idx+1:]
                name = urllib.parse.unquote(name)
                data = data[:idx]

            # 分离用户信息和服务器信息
            parts = data.split('@', 1)
            if len(parts) != 2:
                logger.debug(f"SS格式错误: 缺少@分隔符 - {url}")
                return None

            user_info = self.decode_base64(parts[0])

            # 分离加密方式和密码
            method_and_password = user_info.split(':', 1)
            if len(method_and_password) != 2:
                logger.debug(f"SS格式错误: 加密方式和密码格式不正确 - {url}")
                return None

            method = method_and_password[0]
            password = method_and_password[1]

            # 处理密码：先URL解码
            password = urllib.parse.unquote(password)

            # 对于普通密码，尝试base64解码
            # 对于2022-blake3-aes-128-gcm等新加密方式的复合密码，保持原样
            if not (password.count(':') > 0 and ('=' in password or '%' in password)):
                password = self.decode_base64(password)

            # 分离服务器地址和端口
            host_port = parts[1].split(':', 1)
            if len(host_port) != 2:
                logger.debug(f"SS格式错误: 服务器地址格式不正确 - {url}")
                return None

            try:
                port = int(host_port[1])
            except ValueError:
                logger.debug(f"SS格式错误: 端口格式不正确 - {url}")
                return None

            return {
                'name': name or f"SS-{host_port[0]}:{port}",
                'type': 'ss',
                'server': host_port[0],
                'port': port,
                'cipher': method,
                'password': password
            }
        except Exception as e:
            logger.debug(f"解析SS节点失败: {e} - {url}")
            return None

    def parse_vmess(self, url: str) -> Optional[Dict[str, Any]]:
        """解析VMess节点"""
        if not url.startswith('vmess://'):
            return None

        try:
            url = url[8:]  # 移除 vmess:// 前缀
            url = url.replace('`', '')  # 移除可能的反引号

            decoded = base64.b64decode(url).decode('utf-8')
            vmess_info = json.loads(decoded)

            proxy = {
                'name': vmess_info.get('ps', f"VMess-{vmess_info.get('add', 'unknown')}"),
                'type': 'vmess',
                'server': vmess_info.get('add', ''),
                'port': int(vmess_info.get('port', 443)),
                'uuid': vmess_info.get('id', ''),
                'alterId': int(vmess_info.get('aid', 0)),
                'cipher': 'auto',
                'network': vmess_info.get('net', 'tcp'),
                'tls': vmess_info.get('tls') == 'tls'
            }

            # 添加传输层配置
            if vmess_info.get('net') == 'ws':
                proxy['ws-opts'] = {
                    'path': vmess_info.get('path', '/'),
                    'headers': {'Host': vmess_info.get('host', '')} if vmess_info.get('host') else {}
                }
            elif vmess_info.get('net') == 'grpc':
                proxy['grpc-opts'] = {
                    'serviceName': vmess_info.get('path', '')
                }

            if vmess_info.get('sni'):
                proxy['servername'] = vmess_info.get('sni')

            return proxy
        except Exception as e:
            logger.debug(f"解析VMess节点失败: {e}")
            return None

    def parse_trojan(self, url: str) -> Optional[Dict[str, Any]]:
        """解析Trojan节点"""
        if not url.startswith('trojan://'):
            return None

        try:
            parsed = urllib.parse.urlparse(url)
            password = parsed.username
            server = parsed.hostname
            port = parsed.port
            name = urllib.parse.unquote(parsed.fragment) if parsed.fragment else f"Trojan-{server}:{port}"

            proxy = {
                'name': name,
                'type': 'trojan',
                'server': server,
                'port': port,
                'password': password,
                'sni': parsed.hostname
            }

            # 解析查询参数
            params = urllib.parse.parse_qs(parsed.query)
            if 'sni' in params:
                proxy['sni'] = params['sni'][0]
            if 'allowInsecure' in params and params['allowInsecure'][0] == '1':
                proxy['skip-cert-verify'] = True

            return proxy
        except Exception as e:
            logger.debug(f"解析Trojan节点失败: {e}")
            return None

    def parse_vless(self, url: str) -> Optional[Dict[str, Any]]:
        """解析VLESS节点"""
        if not url.startswith('vless://'):
            return None

        try:
            parsed = urllib.parse.urlparse(url)
            uuid = parsed.username
            server = parsed.hostname
            port = parsed.port
            name = urllib.parse.unquote(parsed.fragment) if parsed.fragment else f"VLESS-{server}:{port}"

            proxy = {
                'name': name,
                'type': 'vless',
                'server': server,
                'port': port,
                'uuid': uuid
            }

            # 解析查询参数
            params = urllib.parse.parse_qs(parsed.query)
            if 'security' in params:
                security = params['security'][0]
                if security == 'tls':
                    proxy['tls'] = True
                elif security == 'reality':
                    proxy['reality-opts'] = {}
                    if 'pbk' in params:
                        proxy['reality-opts']['public-key'] = params['pbk'][0]
                    if 'sid' in params:
                        proxy['reality-opts']['short-id'] = params['sid'][0]

            if 'type' in params:
                network = params['type'][0]
                proxy['network'] = network
                if network == 'ws':
                    proxy['ws-opts'] = {
                        'path': params.get('path', ['/'])[0],
                        'headers': {'Host': params.get('host', [''])[0]} if params.get('host') else {}
                    }

            if 'sni' in params:
                proxy['servername'] = params['sni'][0]

            return proxy
        except Exception as e:
            logger.debug(f"解析VLESS节点失败: {e}")
            return None

    def parse_hysteria2(self, url: str) -> Optional[Dict[str, Any]]:
        """解析Hysteria2节点"""
        if not url.startswith(('hysteria2://', 'hy2://')):
            return None

        try:
            if url.startswith('hysteria2://'):
                url = url[12:]
            else:
                url = url[6:]

            parsed = urllib.parse.urlparse(f"hysteria2://{url}")
            password = parsed.username
            server = parsed.hostname
            port = parsed.port
            name = urllib.parse.unquote(parsed.fragment) if parsed.fragment else f"Hysteria2-{server}:{port}"

            proxy = {
                'name': name,
                'type': 'hysteria2',
                'server': server,
                'port': port,
                'password': password
            }

            # 解析查询参数
            params = urllib.parse.parse_qs(parsed.query)
            if 'sni' in params:
                proxy['sni'] = params['sni'][0]
            if 'insecure' in params and params['insecure'][0] == '1':
                proxy['skip-cert-verify'] = True

            return proxy
        except Exception as e:
            logger.debug(f"解析Hysteria2节点失败: {e}")
            return None

    def parse_proxy(self, proxy_url: str) -> Optional[Dict[str, Any]]:
        """解析代理节点URL"""
        proxy_url = proxy_url.strip()
        if not proxy_url:
            return None

        if proxy_url.startswith('ss://'):
            return self.parse_ss(proxy_url)
        elif proxy_url.startswith('vmess://'):
            return self.parse_vmess(proxy_url)
        elif proxy_url.startswith('trojan://'):
            return self.parse_trojan(proxy_url)
        elif proxy_url.startswith('vless://'):
            return self.parse_vless(proxy_url)
        elif proxy_url.startswith(('hysteria2://', 'hy2://')):
            return self.parse_hysteria2(proxy_url)

        return None

    def deduplicate_proxies(self, proxies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重代理节点（参考项目中的去重逻辑）"""
        seen_keys = {}
        result = []

        for proxy in proxies:
            server = proxy.get('server', '')
            if not server:
                continue

            # 获取服务器名称，优先使用servername，其次使用sni
            servername = proxy.get('servername', '') or proxy.get('sni', '')

            # 获取密码或UUID作为认证信息
            password = proxy.get('password', '') or proxy.get('uuid', '')

            # 获取端口
            port = proxy.get('port', '')

            # 生成去重键，格式与项目中保持一致
            key = f"{server}:{port}:{servername}:{password}"

            if key not in seen_keys:
                seen_keys[key] = True
                result.append(proxy)
            else:
                # 记录重复节点信息用于调试
                logger.debug(f"发现重复节点: {proxy.get('name', 'Unknown')} - {key}")

        logger.info(f"去重统计: 原始节点 {len(proxies)} 个，去重后 {len(result)} 个，移除重复 {len(proxies) - len(result)} 个")
        return result

    def find_txt_files(self, directory: str = '.') -> List[str]:
        """查找当前目录下的所有txt文件"""
        txt_files = []
        for root, _, files in os.walk(directory):
            for file in files:
                if file.lower().endswith('.txt'):
                    txt_files.append(os.path.join(root, file))
        return txt_files

    def extract_base64_from_file(self, file_path: str) -> List[str]:
        """从文件中提取base64编码的文本"""
        base64_strings = []
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 查找可能的base64字符串
            matches = self.base64_pattern.findall(content)
            for match in matches:
                if self.is_base64(match):
                    base64_strings.append(match)

        except Exception as e:
            logger.error(f"读取文件 {file_path} 失败: {e}")

        return base64_strings

    def extract_proxies_from_base64(self, base64_str: str) -> List[Dict[str, Any]]:
        """从base64字符串中提取代理节点"""
        proxies = []
        try:
            decoded = self.decode_base64(base64_str)

            # 按行分割解码后的内容
            lines = decoded.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是代理URL
                if self.proxy_pattern.match(line):
                    proxy = self.parse_proxy(line)
                    if proxy:
                        proxies.append(proxy)

        except Exception as e:
            logger.debug(f"从base64提取代理失败: {e}")

        return proxies

    def process_txt_files(self) -> List[Dict[str, Any]]:
        """处理所有txt文件，提取代理节点"""
        all_proxies = []
        txt_files = self.find_txt_files()

        logger.info(f"找到 {len(txt_files)} 个txt文件")

        for file_path in txt_files:
            logger.info(f"处理文件: {file_path}")

            # 首先尝试直接读取文件内容查找代理URL
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # 直接查找代理URL
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    if self.proxy_pattern.match(line):
                        proxy = self.parse_proxy(line)
                        if proxy:
                            all_proxies.append(proxy)

            except Exception as e:
                logger.error(f"直接读取文件 {file_path} 失败: {e}")

            # 然后提取base64编码的内容
            base64_strings = self.extract_base64_from_file(file_path)
            logger.info(f"从 {file_path} 提取到 {len(base64_strings)} 个base64字符串")

            for base64_str in base64_strings:
                proxies = self.extract_proxies_from_base64(base64_str)
                all_proxies.extend(proxies)
                logger.info(f"从base64字符串解析出 {len(proxies)} 个代理节点")

        return all_proxies

    def generate_yaml_config(self, proxies: List[Dict[str, Any]]) -> str:
        """生成YAML配置文件内容"""
        config = {
            'proxies': proxies,
            'proxy-groups': [
                {
                    'name': 'auto',
                    'type': 'url-test',
                    'proxies': [proxy['name'] for proxy in proxies],
                    'url': 'http://www.gstatic.com/generate_204',
                    'interval': 300
                },
                {
                    'name': 'select',
                    'type': 'select',
                    'proxies': ['auto'] + [proxy['name'] for proxy in proxies]
                }
            ],
            'rules': [
                'MATCH,select'
            ]
        }

        # 生成YAML内容
        yaml_content = yaml.dump(config, default_flow_style=False, allow_unicode=True, sort_keys=False)

        # 修复缩进问题：确保proxies列表项前有两个空格
        lines = yaml_content.split('\n')
        fixed_lines = []
        in_proxies_section = False

        for line in lines:
            if line.startswith('proxies:'):
                in_proxies_section = True
                fixed_lines.append(line)
            elif line.startswith('proxy-groups:') or line.startswith('rules:'):
                in_proxies_section = False
                fixed_lines.append(line)
            elif in_proxies_section and line.startswith('- '):
                # 为proxies列表项添加正确的缩进
                fixed_lines.append('  ' + line)
            elif in_proxies_section and line.startswith('  ') and not line.startswith('  - '):
                # 为proxies列表项的属性保持正确缩进
                fixed_lines.append('  ' + line)
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def save_yaml_file(self, content: str, filename: str = 'proxies.yaml'):
        """保存YAML文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"YAML配置已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存YAML文件失败: {e}")

    def run(self):
        """运行主程序"""
        logger.info("开始处理txt文件中的base64代理节点...")

        # 处理所有txt文件
        all_proxies = self.process_txt_files()
        logger.info(f"总共提取到 {len(all_proxies)} 个代理节点")

        if not all_proxies:
            logger.warning("未找到任何代理节点")
            return

        # 去重
        unique_proxies = self.deduplicate_proxies(all_proxies)
        logger.info(f"去重后剩余 {len(unique_proxies)} 个代理节点")

        # 生成YAML配置
        yaml_content = self.generate_yaml_config(unique_proxies)

        # 保存文件
        self.save_yaml_file(yaml_content)

        # 显示统计信息
        self.print_statistics(unique_proxies)

    def print_statistics(self, proxies: List[Dict[str, Any]]):
        """打印统计信息"""
        if not proxies:
            return

        # 按类型统计
        type_count = {}
        for proxy in proxies:
            proxy_type = proxy.get('type', 'unknown')
            type_count[proxy_type] = type_count.get(proxy_type, 0) + 1

        logger.info("代理节点类型统计:")
        for proxy_type, count in type_count.items():
            logger.info(f"  {proxy_type}: {count} 个")


def main():
    """主函数"""
    converter = ProxyConverter()
    converter.run()


if __name__ == '__main__':
    main()
