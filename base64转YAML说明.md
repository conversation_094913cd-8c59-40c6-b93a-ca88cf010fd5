# Base64代理节点转换器

这是一个Python脚本，用于从txt文件中提取base64编码的代理节点，解码后转换为YAML格式的配置文件。

## 功能特性

1. **自动搜索txt文件**：扫描当前目录及子目录中的所有txt文件
2. **Base64解码**：自动识别并解码base64编码的内容
3. **多协议支持**：支持以下代理协议：
   - Shadowsocks (ss://)
   - VMess (vmess://)
   - Trojan (trojan://)
   - VLESS (vless://)
   - Hysteria2 (hysteria2://, hy2://)
   - SSR (ssr://)
4. **节点去重**：基于服务器地址、端口、密码等信息进行去重
5. **YAML输出**：生成标准的Clash/Mihomo配置文件格式
6. **统计信息**：显示各类型节点的数量统计

## 使用方法

### 前置要求

确保已安装Python 3.6+和所需依赖：

```bash
pip install pyyaml
```

### 运行脚本

1. 将脚本放在包含txt文件的目录中
2. 运行脚本：

```bash
python base64_proxy_converter.py
```

### 输出文件

脚本会在当前目录生成 `proxies.yaml` 文件，包含：
- `proxies`: 所有解析出的代理节点
- `proxy-groups`: 自动生成的代理组（auto和select）
- `rules`: 基本的路由规则

## 测试结果

使用提供的base64.txt文件测试，成功解析出144个代理节点：

```
2025-05-24 18:31:02,538 - INFO - 总共提取到 144 个代理节点
2025-05-24 18:31:02,538 - INFO - 去重后剩余 144 个代理节点
2025-05-24 18:31:02,590 - INFO - 代理节点类型统计:
2025-05-24 18:31:02,590 - INFO -   ss: 54 个
2025-05-24 18:31:02,590 - INFO -   trojan: 45 个
2025-05-24 18:31:02,590 - INFO -   hysteria2: 13 个
2025-05-24 18:31:02,590 - INFO -   vmess: 16 个
2025-05-24 18:31:02,590 - INFO -   vless: 16 个
```

## 代码特点

### 参考现有项目代码

脚本设计时参考了当前项目中的代理处理逻辑：

1. **Base64解码逻辑**：参考了 `proxy/parser/base64.go` 中的解码实现
2. **节点解析**：参考了 `proxy/parser/` 目录下各协议的解析器
3. **去重功能**：参考了 `proxy/dedup.go` 中的去重逻辑
4. **YAML生成**：参考了项目中的配置文件格式

### 解析器实现

- **Shadowsocks解析器**：处理ss://格式，支持base64编码的用户信息
- **VMess解析器**：解析vmess://格式的JSON配置
- **Trojan解析器**：处理trojan://格式，支持查询参数
- **VLESS解析器**：支持VLESS协议，包括Reality配置
- **Hysteria2解析器**：处理hysteria2://和hy2://格式

### 去重算法

使用与项目相同的去重逻辑：
```python
key = f"{server}:{port}:{servername}:{password}"
```

基于服务器地址、端口、服务器名称和密码生成唯一键进行去重。

## 配置文件格式

生成的YAML文件符合Clash/Mihomo标准格式，包含：

```yaml
proxies:
  - name: "节点名称"
    type: "协议类型"
    server: "服务器地址"
    port: 端口
    # 其他协议特定配置...

proxy-groups:
  - name: "auto"
    type: "url-test"
    proxies: [所有节点]
    url: "http://www.gstatic.com/generate_204"
    interval: 300
  - name: "select"
    type: "select"
    proxies: ["auto", 所有节点]

rules:
  - "MATCH,select"
```

## 注意事项

1. 脚本会自动处理URL安全的base64编码（替换-和_字符）
2. 支持不完整的base64填充自动补全
3. 对于无法解析的节点会记录调试信息但不会中断处理
4. 生成的节点名称会包含国家/地区标识和速度信息（如果原始数据中包含）

## 扩展功能

如需添加新的协议支持，可以在 `ProxyConverter` 类中添加相应的解析方法，并在 `parse_proxy` 方法中注册新的协议前缀。
