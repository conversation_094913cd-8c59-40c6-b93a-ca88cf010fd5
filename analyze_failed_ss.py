#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析失败的SS节点，找出解析失败的原因
"""

import base64
import urllib.parse

# 失败的5个SS节点
failed_nodes = [
    "ss://YWVzLTEyOC1nY206c2FkdWppaiFAZGlRb2pkMTI1NA%3D%3D@59.42.247.187:49759#%F0%9F%87%AD%F0%9F%87%B0%20%E9%A6%99%E6%B8%AF%20002",
    "ss://<EMAIL>:15013#%F0%9F%87%AD%F0%9F%87%B0%20%E9%A6%99%E6%B8%AF%20019",
    "ss://<EMAIL>:15010#%F0%9F%87%AD%F0%9F%87%B0%20%E9%A6%99%E6%B8%AF%20020",
    "ss://<EMAIL>:44223#%F0%9F%87%BA%F0%9F%87%B8%20%E7%BE%8E%E5%9B%BD%20008",
    "ss://<EMAIL>:15008#%F0%9F%87%AD%F0%9F%87%B0%20%E9%A6%99%E6%B8%AF%20018"
]

def decode_base64_safe(s):
    """安全解码base64"""
    try:
        # 处理URL编码
        s = urllib.parse.unquote(s)
        
        # 处理URL安全的base64
        s = s.replace('-', '+').replace('_', '/')
        
        # 添加缺失的填充
        if len(s) % 4 != 0:
            s += '=' * (4 - len(s) % 4)
        
        decoded = base64.b64decode(s)
        return decoded.decode('utf-8')
    except Exception as e:
        print(f"解码失败: {e}")
        return None

def analyze_ss_node(url):
    """分析SS节点"""
    print(f"\n分析节点: {url[:80]}...")
    
    if not url.startswith('ss://'):
        print("不是SS节点")
        return
    
    data = url[5:]  # 移除 ss:// 前缀
    
    # 分离名称部分
    name = ""
    if '#' in data:
        idx = data.rfind('#')
        name = data[idx+1:]
        name = urllib.parse.unquote(name)
        data = data[:idx]
        print(f"节点名称: {name}")
    
    print(f"用户信息+服务器信息: {data}")
    
    # 检查是否包含@分隔符
    if '@' not in data:
        print("没有@分隔符，尝试整体解码...")
        decoded_data = decode_base64_safe(data)
        if decoded_data:
            print(f"解码后: {decoded_data}")
            data = decoded_data
        else:
            print("解码失败")
            return
    
    if '@' not in data:
        print("解码后仍然没有@分隔符")
        return
    
    # 分离用户信息和服务器信息
    parts = data.split('@', 1)
    user_info = parts[0]
    server_info = parts[1]
    
    print(f"用户信息: {user_info}")
    print(f"服务器信息: {server_info}")
    
    # 尝试解码用户信息
    decoded_user_info = decode_base64_safe(user_info)
    if decoded_user_info:
        print(f"解码后用户信息: {decoded_user_info}")
        user_info = decoded_user_info
    
    # 分离加密方式和密码
    if ':' not in user_info:
        print("用户信息中没有:分隔符")
        return
    
    method_and_password = user_info.split(':', 1)
    method = method_and_password[0]
    password = method_and_password[1]
    
    print(f"加密方式: {method}")
    print(f"密码: {password}")
    
    # 分离服务器地址和端口
    host_port = server_info.split(':', 1)
    if len(host_port) != 2:
        print("服务器信息格式不正确")
        return
    
    try:
        port = int(host_port[1])
        print(f"服务器: {host_port[0]}")
        print(f"端口: {port}")
        print("✓ 解析成功!")
    except ValueError:
        print("端口格式不正确")

if __name__ == '__main__':
    for i, node in enumerate(failed_nodes, 1):
        print(f"\n{'='*60}")
        print(f"失败节点 {i}:")
        analyze_ss_node(node)
